# grafana-datasources.yaml
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDatasource
metadata:
  name: grafana-datasource-mimir  
spec:
  instanceSelector:
    matchLabels:
      dashboards: grafana
      #app: grafana
  allowCrossNamespaceImport: true
  datasource:
    name: Mimir
    type: prometheus
    access: server
    url: http://grafana-mimir-nginx.infrastructure.svc:80/prometheus
    isDefault: true      
    uid: Mimir #setting stable uuid for using in alerts
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDatasource
metadata:
  name: grafana-datasource-loki  
spec:
  instanceSelector:
    matchLabels:
      dashboards: grafana
      #app: grafana
  allowCrossNamespaceImport: true    
  datasource:
    name: Loki
    type: loki
    access: server
    url: http://grafana-loki-gateway.infrastructure.svc
    isDefault: false
    editable: true
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDatasource
metadata:
  name: grafana-datasource-tempo  
spec:
  instanceSelector:
    matchLabels:
      dashboards: grafana
      #app: grafana
  allowCrossNamespaceImport: true
  datasource:
    name: Tempo
    type: tempo
    uid: tempo
    access: server
    editable: true
    url: http://grafana-tempo.infrastructure.svc.cluster.local:3200
    basicAuth: false
    jsonData:
      httpMethod: POST
      tracesToLogsV2:
        datasourceUid: Loki
        spanStartTimeShift: "-1h"
        spanEndTimeShift: "1h"
      tracesToMetrics:
        datasourceUid: Mimir
        spanStartTimeShift: '-1h'
        spanEndTimeShift: '1h'

# apiVersion: grafana.integreatly.org/v1beta1
# kind: GrafanaDatasource
# metadata:
#   name: grafana-datasources
#   labels:
#     grafana_datasource: "true"  
# spec:
#   instanceSelector:
#     matchLabels:
#       dashboards: "grafana"  
#   datasources:
#     - name: Mimir
#       type: prometheus
#       access: server
#       url: http://grafana-mimir-nginx.infrastructure.svc:80/prometheus
#       isDefault: true      
#     - name: Loki
#       type: loki
#       access: server
#       url: http://grafana-loki-gateway.infrastructure.svc
#       isDefault: false
#       editable: true
#     - name: Tempo
#       type: tempo
#       uid: tempo
#       access: server
#       editable: true
#       url: http://grafana-tempo.infrastructure.svc.cluster.local:3200
#       basicAuth: false
#       jsonData:
#         httpMethod: POST
#         tracesToLogsV2:
#           datasourceUid: Loki
#           spanStartTimeShift: "-1h"
#           spanEndTimeShift: "1h"
#         tracesToMetrics:
#           datasourceUid: Mimir
#           spanStartTimeShift: '-1h'
#           spanEndTimeShift: '1h'
  
# # apiVersion: v1
# # kind: ConfigMap
# # metadata:
# #   name: grafana-datasources
# #   namespace: infrastructure
# #   labels:
# #     grafana_datasource: "true"
# # data:
# #   datasources.yaml: |-
# #     apiVersion: 1
# #     datasources:
# #       - name: Mimir
# #         type: prometheus
# #         access: server
# #         url: http://grafana-mimir-nginx.infrastructure.svc:80/prometheus
# #         isDefault: true      
# #       - name: Loki
# #         type: loki
# #         access: server
# #         url: http://grafana-loki-gateway.infrastructure.svc
# #         isDefault: false
# #         editable: true
# #       - name: Tempo
# #         type: tempo
# #         uid: tempo
# #         access: server
# #         editable: true
# #         url: http://grafana-tempo.infrastructure.svc.cluster.local:3200
# #         basicAuth: false
# #         jsonData:
# #           httpMethod: POST
# #           tracesToLogsV2:
# #             datasourceUid: Loki
# #             spanStartTimeShift: "-1h"
# #             spanEndTimeShift: "1h"
# #           tracesToMetrics:
# #             datasourceUid: Mimir
# #             spanStartTimeShift: '-1h'
# #             spanEndTimeShift: '1h'
