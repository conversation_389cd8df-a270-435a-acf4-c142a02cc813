#helm repo add grafana https://grafana.github.io/helm-charts
#helm repo update
#helm install --namespace monitoring grafana-alloy grafana/alloy -f alloy-values.yaml --create-namespace
#helm upgrade --namespace monitoring grafana-alloy grafana/alloy -f alloy-values.yaml

# Keep CRDs installed
crds:
  create: true                            # :contentReference[oaicite:0]{index=0}

alloy:
  # Run as a single instance or cluster
  clustering:
    enabled: true
    name: "eks-observability-nonprod"

  # # Additional CLI args to the `alloy run` command
  # extraArgs:
  #   - "--log.level=debug"

  # # Mount host logs (you chose to NOT mount /var/log by default for alloy-logs)
  mounts:
    varlog: true
    #dockercontainers: false

#need to fix issue with kustomization
  configMapGenerator:
    - name: alloy
      files:
        - config.alloy
      options:
        disableNameSuffixHash: true

  # Override the default ConfigMap with a custom River config
  configMap:
    create: true
    content: |-
      logging {
        level = "info"
        format = "logfmt"
      }
      //DISCOVER
      discovery.kubernetes "pods" {
        role = "pod"
      }
      discovery.kubernetes "nodes" {
        role = "node"
      }
    
      discovery.relabel "nodes" {
        targets = discovery.kubernetes.nodes.targets
        rule {
          source_labels = ["__meta_kubernetes_node_name"]
          target_label  = "node"
        }
    
        rule {
          replacement = "kubernetes"
          target_label = "source"
        }
      }
      // cAdvisor
      discovery.relabel "cadvisor" {
        targets = discovery.relabel.nodes.output
        rule {
          replacement   = "/metrics/cadvisor"
          target_label  = "__metrics_path__"
        }
      }
      
      // Kubelet
      discovery.relabel "kubelet" {
        targets = discovery.relabel.nodes.output
      }
    
      // api 
      discovery.kubernetes "apiserver" {
        role = "endpoints"
    
        selectors {
          role = "endpoints"
          field = "metadata.name=kubernetes"
        }
    
        namespaces {
          names = ["default"]
        }
      }
    
      // KubeDNS
      discovery.kubernetes "kube_dns" {
        role = "endpoints"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "endpoints"
          label = "k8s-app=kube-dns"
        }
      }

      //kube proxy 

      discovery.kubernetes "kube_proxy" {
        role = "pod"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "pod"
          label = "k8s-app=kube-proxy"
        }
      }

      discovery.relabel "kube_proxy" {
        targets = discovery.kubernetes.kube_proxy.targets
        rule {
          source_labels = ["__address__"]
          replacement = "$1:10249"
          target_label = "__address__"
        }
      }

      // Node Exporter
      discovery.kubernetes "node_exporter" {
        role = "pod"

        selectors {
          role = "pod"
          label = "app.kubernetes.io/name=prometheus-node-exporter"
        }
        namespaces {
          names = ["monitoring"]
        }
      }

      // kube-state-metrics
      discovery.kubernetes "kube_state_metrics" {
        role = "endpoints"

        selectors {
          role = "endpoints"
          label = "app.kubernetes.io/name=kube-state-metrics"
        }
        namespaces {
          names = ["monitoring"]
        }
      }
      //-----------------------------------
      // METRICS
      //-----------------------------------
      prometheus.scrape "kubelet" {
        targets  = discovery.relabel.kubelet.output
        job_name = "integrations/kubernetes/kubelet"
        scheme   = "https"
        scrape_interval = "30s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
    
        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }
    
        clustering {
          enabled = true //should block duplicated samples
        }
    
        forward_to = [prometheus.remote_write.default.receiver]
      }
    
    
      prometheus.scrape "cadvisor" {
        targets = discovery.relabel.cadvisor.output
        job_name = "integrations/kubernetes/cadvisor"
        scheme = "https"
        scrape_interval = "30s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
    
        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }
    
        clustering {
          enabled = true
        }
    
        forward_to = [prometheus.remote_write.default.receiver]
      }
      
      prometheus.scrape "apiserver" {
        targets = discovery.kubernetes.apiserver.targets
        job_name = "integrations/kubernetes/kube-apiserver"
        scheme = "https"
        scrape_interval = "30s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
    
        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = false
          server_name = "kubernetes"
        }
    
        clustering {
          enabled = true
        }
    
        forward_to = [prometheus.remote_write.default.receiver]
      }
    
      prometheus.scrape "kube_dns" {
        targets = discovery.kubernetes.kube_dns.targets
        job_name = "integrations/kubernetes/kube-dns"
        scheme = "http"
        scrape_interval = "30s"
        clustering {
          enabled = true
        }
        forward_to = [prometheus.remote_write.default.receiver]
      }
    
    
      prometheus.scrape "kube_proxy" {
        targets           = discovery.relabel.kube_proxy.output
        job_name          = "integrations/kubernetes/kube-proxy"
        scheme            = "http"
        scrape_interval   = "30s"
        clustering {
          enabled = true
        }
        forward_to = [prometheus.remote_write.default.receiver]
      }

      // Scrape Karpenter metrics every 30s and forward into your Mimir remote-write
      prometheus.scrape "karpenter_metrics" {
        job_name = "integrations/karpenter"
        targets = [
          { __address__ = "karpenter.karpenter.svc.cluster.local:8080" },
        ]
        //targets =  discovery.relabel.karpenter_filter.output
        scheme           = "http"
        scrape_interval = "30s"
        metrics_path    = "/metrics"

        clustering { //should block duplicated samples
          enabled = true
        }         
        honor_timestamps = false   //try to fix sample reject error

        //bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        tls_config {
          insecure_skip_verify = true
        }

        forward_to = [ prometheus.remote_write.default.receiver ]
        //forward_to = argument.metrics_destinations.value
      } 
    

      prometheus.scrape "metrics_server" {
        job_name = "integrations/kubernetes/metrics-server"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        tls_config { insecure_skip_verify = true }
        
        clustering { 
           enabled = true
        }         
        targets = [
          {
            __address__ = "metrics-server.kube-system.svc.cluster.local:443",
            __scheme__  = "https",            
          },
          //{
          //  __address__ = "mimir:8080",
          //  __scheme__  = "https",
          //},
          //{
          //  __address__      = "custom-application:80",
          //  __metrics_path__ = "/custom-metrics-path",
          //},
          //{
          //  __address__ = "alloy:12345",
          //  application = "alloy",
          //  environment = "production",
          //},
        ]
        forward_to = [prometheus.remote_write.default.receiver]
      }      

      discovery.relabel "node_exporter" {
        targets = discovery.kubernetes.node_exporter.targets

        // keep only the specified metrics port name, and pods that are Running and ready
        rule {
          source_labels = [
            "__meta_kubernetes_pod_container_port_name",
            "__meta_kubernetes_pod_container_init",
            "__meta_kubernetes_pod_phase",
            "__meta_kubernetes_pod_ready",
          ]
          separator = "@"
          regex = "metrics@false@Running@true"
          action = "keep"
        }

        // Set the instance label to the node name
        rule {
          source_labels = ["__meta_kubernetes_pod_node_name"]
          action = "replace"
          target_label = "instance"
        }

        // set the namespace label
        rule {
          source_labels = ["__meta_kubernetes_namespace"]
          target_label  = "namespace"
        }

        // set the pod label
        rule {
          source_labels = ["__meta_kubernetes_pod_name"]
          target_label  = "pod"
        }

        // set the container label
        rule {
          source_labels = ["__meta_kubernetes_pod_container_name"]
          target_label  = "container"
        }

        // set a workload label
        rule {
          source_labels = [
            "__meta_kubernetes_pod_controller_kind",
            "__meta_kubernetes_pod_controller_name",
          ]
          separator = "/"
          target_label  = "workload"
        }
        // remove the hash from the ReplicaSet
        rule {
          source_labels = ["workload"]
          regex = "(ReplicaSet/.+)-.+"
          target_label  = "workload"
        }
      }

      prometheus.scrape "node_exporter" {
        //targets = discovery.kubernetes.node_exporter.targets
        targets = discovery.relabel.node_exporter.output
        job_name = "integrations/kubernetes/node_exporter"
        scrape_interval = "30s"
        scheme = "http"
        bearer_token_file = ""
        tls_config {
          insecure_skip_verify = true
        }

        clustering {
          enabled = true
        }
        forward_to = [prometheus.remote_write.default.receiver]
      }


      discovery.relabel "kube_state_metrics" {
        targets = discovery.kubernetes.kube_state_metrics.targets

        // only keep targets with a matching port name
        rule {
          source_labels = ["__meta_kubernetes_pod_container_port_name"]
          regex = "http"
          action = "keep"
        }

        rule {
          action = "replace"
          replacement = "kubernetes"
          target_label = "source"
        }

      }

      prometheus.scrape "kube_state_metrics" {
        targets = discovery.relabel.kube_state_metrics.output
        job_name = "integrations/kubernetes/kube-state-metrics"
        scrape_interval = "30s"
        scheme = "http"
        bearer_token_file = ""
        tls_config {
          insecure_skip_verify = true
        }

        clustering {
          enabled = true
        }
        forward_to = [prometheus.remote_write.default.receiver]
      }


      prometheus.remote_write "default" {
        endpoint { 
          url = "http://**********:30080/api/v1/push" 
           
          //added in order to identify metrics in bucket and logs  
          basic_auth {  
            username = "eks-observability-nonprod-alloy-user"
            password = ""
            // alternatively, for improved security:
            // password_file = "/path/to/secret/file"
          }          
          //headers = {
          //  "X-Scope-OrgID" = "eks-observability-nonprod-alloy-user",
          //}          
          tls_config {
            insecure_skip_verify = false
          }
          send_native_histograms = false
          queue_config {
            capacity = 10000
            min_shards = 1
            max_shards = 50
            max_samples_per_send = 2000
            batch_send_deadline = "5s"
            min_backoff = "30ms"
            max_backoff = "5s"
            retry_on_http_429 = true
            sample_age_limit = "0s"
          }
          write_relabel_config {
            source_labels = ["cluster"]
            regex = ""
            replacement = "eks-observability-nonprod"
            target_label = "cluster"
          }
          write_relabel_config {
            source_labels = ["k8s_cluster_name"]
            regex = ""
            replacement = "eks-observability-nonprod"
            target_label = "k8s_cluster_name"
          }
        }
        //external_labels = {
        //  cluster = "eks-observability-nonprod",
       // }
        wal {
          truncate_frequency = "2h"
          min_keepalive_time = "5m"
          max_keepalive_time = "8h"
        }
      }
    
    
      //-----------------------------------
      // LOGS
      //-----------------------------------
    
      //loki.source.kubernetes_events "clusterEvents" {
      //  forward_to = [loki.write.default.receiver]
      //}
    
      // collect Kubernetes events as logs
      loki.source.kubernetes_events "k8s_events" {
        job_name   = "integrations/kubernetes/eventhandler"
        log_format = "logfmt"
        forward_to = [ loki.process.k8s_events.receiver ]
      }      
      // process and write events into Loki
      loki.process "k8s_events" {
        forward_to = [ loki.write.default.receiver ]
        stage.labels {
          values = { kubernetes_cluster_events = "job" }
        }
      }
    
    
      loki.source.journal "worker" {
        //job_name   = "integrations/kubernetes/journal"
        path = "/var/log/journal"
        format_as_json = false
        max_age = "8h"        
        labels = {
          job = "integrations/kubernetes/journal",
          //instance = sys.env("HOSTNAME"),
        }
        forward_to = [loki.write.default.receiver]
      }
    
      discovery.relabel "pod_logs" {
        targets = discovery.kubernetes.pods.targets
      
        // Label creation - "namespace" field from "__meta_kubernetes_namespace"
        rule {
          source_labels = ["__meta_kubernetes_namespace"]
          action = "replace"
          target_label = "namespace"
        }
      
        // Label creation - "pod" field from "__meta_kubernetes_pod_name"
        rule {
          source_labels = ["__meta_kubernetes_pod_name"]
          action = "replace"
          target_label = "pod"
        }
      
        // Label creation - "container" field from "__meta_kubernetes_pod_container_name"
        rule {
          source_labels = ["__meta_kubernetes_pod_container_name"]
          action = "replace"
          target_label = "container"
        }
      
        // Label creation -  "app" field from "__meta_kubernetes_pod_label_app_kubernetes_io_name"
        rule {
          source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
          action = "replace"
          target_label = "app"
        }
      
        // Label creation -  "job" field from "__meta_kubernetes_namespace" and "__meta_kubernetes_pod_container_name"
        // Concatenate values __meta_kubernetes_namespace/__meta_kubernetes_pod_container_name
        rule {
          source_labels = ["__meta_kubernetes_namespace", "__meta_kubernetes_pod_container_name"]
          action = "replace"
          target_label = "job"
          separator = "/"
          replacement = "$1"
        }
      
        // Label creation - "container" field from "__meta_kubernetes_pod_uid" and "__meta_kubernetes_pod_container_name"
        // Concatenate values __meta_kubernetes_pod_uid/__meta_kubernetes_pod_container_name.log
        rule {
          source_labels = ["__meta_kubernetes_pod_uid", "__meta_kubernetes_pod_container_name"]
          action = "replace"
          target_label = "__path__"
          separator = "/"
          replacement = "/var/log/pods/*$1/*.log"
        }
      
        // Label creation -  "container_runtime" field from "__meta_kubernetes_pod_container_id"
        rule {
          source_labels = ["__meta_kubernetes_pod_container_id"]
          action = "replace"
          target_label = "container_runtime"
          regex = "^(\\S+):\\/\\/.+$"
          replacement = "$1"
        }
      }
      
      // loki.source.kubernetes tails logs from Kubernetes containers using the Kubernetes API.
      loki.source.kubernetes "pod_logs" {
        targets    = discovery.relabel.pod_logs.output
        forward_to = [loki.process.pod_logs.receiver]
      }
      
      // loki.process receives log entries from other Loki components, applies one or more processing stages,
      // and forwards the results to the list of receivers in the component's arguments.
      loki.process "pod_logs" {
        stage.static_labels {
            values = {
              cluster = "eks-observability-nonprod",
            }
        }
      
        forward_to = [loki.write.default.receiver]
      }
    
      loki.write "default" {
        endpoint {
           url = "http://**********:31045/loki/api/v1/push"
           tls_config {
             insecure_skip_verify = false
           }
           min_backoff_period = "500ms"
           max_backoff_period = "5m"
           max_backoff_retries = "10"
         }                              
        external_labels = {
          "cluster" = "eks-observability-nonprod",
          "k8s_cluster_name" = "eks-observability-nonprod",
        }
      }
    
      
   

# Use the default DaemonSet controller
controller:
  type: daemonset
  tolerations:
    - key: "CriticalAddonsOnly"
      operator: "Exists"
      effect: "NoSchedule"

service:
  enabled: true

