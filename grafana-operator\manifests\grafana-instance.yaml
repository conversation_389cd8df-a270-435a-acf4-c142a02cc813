apiVersion: grafana.integreatly.org/v1beta1
kind: Grafana
metadata:
  name: grafana  
  labels:
    dashboards: grafana
    app: grafana-ui-operator
spec:  
  persistentVolumeClaim:
    spec:
      storageClassName: "ebs-sc-gp3"
      #storageClassName: "ebs-sc"
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: 5Gi

  config:
    security:
      admin_user: admin
      # admin_password:
      #   valueFrom:
      #     secretKeyRef:
      #       name: grafana-ui-admin-password
      #       key: password
    # auth:
    #   azureAD:
    #     enabled: true
    #     allow_sign_up: true
    #     client_id: ${GF_AUTH_AZUREAD_CLIENT_ID}
    #     client_secret:
    #       valueFrom:
    #         secretKeyRef:
    #           name: grafana-azuread-auth
    #           key: client-secret
    #     scopes:
    #       - openid
    #       - email
    #       - profile
    #     auth_url: https://login.microsoftonline.com/<TENANT_ID>/oauth2/v2.0/authorize
    #     token_url: https://login.microsoftonline.com/<TENANT_ID>/oauth2/v2.0/token
    #     allowed_organizations:
    #       - <TENANT_ID>

  deployment:
    # strategy:
    #   type: Recreate
    spec:
      template:
        spec:
          securityContext:
            fsGroup: 472
            supplementalGroups: [0]
          containers:
            - name: grafana
              image: grafana/grafana:latest
              imagePullPolicy: IfNotPresent
              env:
              - name: GF_SECURITY_ADMIN_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: grafana-ui-admin-password
                    key: password              
              # volumeMounts:
              #   - mountPath: /var/lib/grafana
              #     name: grafana-data 
          volumes:
            #- name: grafana-pv
            - name: grafana-data
              persistentVolumeClaim:
                claimName: grafana-pvc                    
              # ports:
              #   - containerPort: 3000
              #     name: http
              # readinessProbe:
              #   httpGet:
              #     path: /robots.txt
              #     port: http
              # livenessProbe:
              #   tcpSocket:
              #     port: http
          
          # Resource requests/limits
          resources:
            requests:
              cpu: 1
              memory: 750Mi
            limits:            
              memory: 2Gi              

  # # Inject grafana.ini and datasources via sidecar
  # sidecar:
  #   # will mount all ConfigMaps labeled grafana_dashboard=true
  #   dashboards:
  #     enabled: true      
  #     label: grafana_dashboard
  #     folder: ''
  #     searchNamespace: infrastructure
  #   # will mount all ConfigMaps labeled grafana_datasource=true
  #   datasources:
  #     enabled: true                     
  #     label: grafana_datasource         # match CM’s label key
  #     labelValue: "true"                # match CM’s label value
  #     searchNamespace: infrastructure   # CM NS

  # Azure AD and admin creds



