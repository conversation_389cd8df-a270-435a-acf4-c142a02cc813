# operator-values.yaml
# ─────────────────────
# Deploy the operator into your existing `infrastructure` namespace
# and give it permissions to watch that namespace.
#watchNamespaces: infrastructure
####watchNamespace: infrastructure



leaderElect: true
replicas: 2


serviceMonitor:
  # -- Whether to create a ServiceMonitor
  enabled: true

dashboard:
  # -- Whether to create a ConfigMap containing a dashboard monitoring the operator metrics.
  # Consider enabling this if you are enabling the ServiceMonitor.
  # Optionally, a GrafanaDashboard CR can be manually created pointing to the Grafana.com dashboard ID 22785
  # https://grafana.com/grafana/dashboards/22785-grafana-operator/
  # The Grafana.com dashboard is maintained by the community and does not necessarily match the JSON definition in this repository.
  enabled: true

# extraEnv:
#   - name: WATCH_NAMESPACE
#     value: infrastructure

# watchNamespaceSelector: ""

# watchLabelSelectors:
#   - app=grafana

# Create RBAC and ServiceAccount objects
rbac:
  create: true

serviceAccount:
  create: true

image:
  # -- grafana operator image repository
  repository: ghcr.io/grafana/grafana-operator
  # -- The image pull policy to use in grafana operator container
  pullPolicy: IfNotPresent

metricsService:
  # -- metrics service type
  type: ClusterIP
  # -- metrics service port
  metricsPort: 9090
  # -- port for the pprof profiling endpoint
  pprofPort: 8888
# -- additional labels to add to all resources

# Give the operator a small footprint
resources:
  requests:
    cpu: 100m
    memory: 256Mi
  limits:
    cpu: 750m
    memory: 1Gi
